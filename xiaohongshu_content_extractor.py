from playwright.sync_api import sync_playwright
import time
import os
import requests
import sys
import re
import json
from urllib.parse import urlparse
from datetime import datetime
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DOWNLOAD_IMAGES = True
IMAGE_FOLDER = "xiaohongshu_images"
MAX_POSTS = 5  # Maximum number of posts to process

# Initialize OpenAI client
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
openai_client = OpenAI(api_key=OPENAI_API_KEY)

def setup_browser():
    """Set up the browser with stealth mode"""
    playwright = sync_playwright().start()

    browser = playwright.chromium.launch(
        headless=False,
        args=[
            "--window-size=1920,1080",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security"
        ]
    )

    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    )

    # Add script to bypass detection
    context.add_init_script("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined
        });
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en', 'zh-CN'],
        });
    """)

    return playwright, browser, context

def login_to_xiaohongshu(page):
    """Handle login to Xiaohongshu"""
    # Navigate to homepage
    print("Navigating to Xiaohongshu homepage...")
    page.goto("https://www.xiaohongshu.com/", wait_until="domcontentloaded", timeout=30000)
    time.sleep(5)

    # Simple check for login status - just look for "我" in sidebar
    print("Checking if already logged in...")
    if page.query_selector("text=我"):
        print("Found '我' in sidebar - already logged in")
        print("Continuing with extraction...")
        return True

    print("Not logged in, proceeding with login process...")

    # Click login button
    print("Clicking login button...")
    login_selectors = [
        "text=登录",
        "button:has-text('登录')",
        "div:has-text('登录'):not(:has-div)",
        "a:has-text('登录')",
        ".login-button",
        ".sign-in-button",
        "[data-testid='login-button']"
    ]

    login_clicked = False
    for selector in login_selectors:
        try:
            if page.query_selector(selector):
                page.click(selector, timeout=5000)
                print(f"Clicked login button using selector: {selector}")
                login_clicked = True
                break
        except Exception:
            continue

    if not login_clicked:
        print("Could not find login button automatically")
        print("Please click the login button manually...")
        input("Press Enter after clicking the login button...")

    # Wait for QR code
    try:
        page.wait_for_selector("div[class*='qrcode'], img[class*='qrcode']", timeout=10000)
        print("Please scan the QR code with your phone...")
    except:
        print("QR code not detected automatically")
        print("Please proceed with manual login...")

    # Wait for user to complete login
    print("Please complete the login process...")
    print("IMPORTANT: Press Enter ONLY AFTER you've successfully logged in")
    input("Press Enter when you've completed the login process...")
    time.sleep(5)

    # Simple verification - just check for "我" in sidebar
    if page.query_selector("text=我"):
        print("Login successful - found '我' in sidebar")
        print("Please confirm login was successful...")
        input("Press Enter to continue after confirming login...")
        return True

    print("Login verification failed - '我' not found in sidebar")
    print("Please confirm if login was successful anyway...")
    input("Press Enter to continue if you're logged in...")
    # Take a screenshot to help with debugging
    page.screenshot(path="login_verification_failed.png")
    return True

def navigate_to_liked_posts(page):
    """Navigate to the liked posts page using a two-step process: click '我' then click '点赞'"""
    print("Navigating to liked posts tab (two-step process)...")

    # Step 1: Click on the "我" (Me) element in the sidebar
    print("Step 1: Clicking on the '我' (Me) element in the sidebar...")

    # Try to locate and click the sidebar menu item with text "我"
    sidebar_me_clicked = False

    # Based on the screenshot, try more specific selectors targeting the sidebar channel list
    sidebar_me_selectors = [
        # Target the sidebar channel list items
        "div.side-bar ul li:has-text('我')",
        "div[class*='side'] ul li:has-text('我')",
        "div[class*='channel'] li:has-text('我')",
        "div[class*='nav'] li:has-text('我')",
        # Target the specific element structure shown in the screenshot
        "div.side-bar div.channel-list div:has-text('我')",
        "div[class*='side'] div[class*='channel'] div:has-text('我')",
        # More general selectors
        "text=我",
        "li:has-text('我')",
        "a:has-text('我')",
        # XPath selectors
        "xpath=//div[contains(@class, 'side')]//li[contains(text(), '我')]",
        "xpath=//div[contains(@class, 'channel')]//div[contains(text(), '我')]",
        "xpath=//ul//li[contains(text(), '我')]"
    ]

    for selector in sidebar_me_selectors:
        try:
            if selector.startswith("xpath="):
                xpath = selector.replace("xpath=", "")
                elements = page.query_selector_all(f"xpath={xpath}")
                if elements:
                    for element in elements:
                        text = element.text_content().strip()
                        if text == "我" or "我" in text and len(text) <= 3:
                            element.click()
                            print(f"Clicked '我' using xpath selector: {selector}")
                            sidebar_me_clicked = True
                            time.sleep(5)  # Wait for the profile page to load
                            break
            else:
                elements = page.query_selector_all(selector)
                if elements:
                    for element in elements:
                        text = element.text_content().strip()
                        if text == "我" or "我" in text and len(text) <= 3:
                            element.click()
                            print(f"Clicked '我' using selector: {selector}")
                            sidebar_me_clicked = True
                            time.sleep(5)  # Wait for the profile page to load
                            break

            if sidebar_me_clicked:
                break

        except Exception as e:
            print(f"Failed with selector {selector}: {e}")

    # If we still couldn't click, try a more visual approach - look for elements in the left sidebar
    if not sidebar_me_clicked:
        try:
            # Try to find elements on the left side of the page (first 20% of width)
            page_width = page.viewport_size["width"]
            left_boundary = page_width * 0.2

            # Get all elements with text "我"
            elements = page.query_selector_all("text=我")
            for element in elements:
                # Check if the element is in the left sidebar
                bounding_box = element.bounding_box()
                if bounding_box and bounding_box["x"] < left_boundary:
                    element.click()
                    print(f"Clicked '我' using visual position in left sidebar")
                    sidebar_me_clicked = True
                    time.sleep(5)
                    break
        except Exception as e:
            print(f"Failed with visual position approach: {e}")

    # If we still couldn't click '我', try direct navigation to profile
    if not sidebar_me_clicked:
        print("Could not click '我' element, trying direct navigation to profile...")
        page.goto("https://www.xiaohongshu.com/user/profile", wait_until="domcontentloaded", timeout=30000)
        time.sleep(5)

    # Step 2: Now that we're on the profile page, click on the "点赞" (Likes) tab
    print("Step 2: Clicking on the '点赞' (Likes) tab...")

    # Try to locate and click the "点赞" tab
    liked_tab_clicked = False

    # Wait for the profile page to fully load
    time.sleep(3)

    # Try with a variety of selectors for the "点赞" tab based on the screenshot
    liked_tab_selectors = [
        # Based on the screenshot structure
        "div.tabs > div:has-text('点赞')",
        "div.tab-list > div:has-text('点赞')",
        "div[class*='tab-container'] div:has-text('点赞')",
        "div[class*='tab-bar'] div:has-text('点赞')",
        # The specific element shown in the screenshot
        "div.tab-item:has-text('点赞')",
        "div.tab:has-text('点赞')",
        # More general selectors
        "text=点赞",
        "a:has-text('点赞')",
        "div:has-text('点赞')",
        # XPath selectors targeting the structure in the screenshot
        "xpath=//div[contains(@class, 'tab') and contains(text(), '点赞')]",
        "xpath=//div[contains(@class, 'tab-item') and contains(text(), '点赞')]",
        "xpath=//div[contains(@class, 'tab-container')]//div[contains(text(), '点赞')]",
        # More general XPath selectors
        "xpath=//div[contains(text(), '点赞')]",
        "xpath=//a[contains(text(), '点赞')]"
    ]

    for selector in liked_tab_selectors:
        try:
            if selector.startswith("xpath="):
                xpath = selector.replace("xpath=", "")
                elements = page.query_selector_all(f"xpath={xpath}")
                if elements:
                    for element in elements:
                        text = element.text_content().strip()
                        if text == "点赞" or "点赞" in text and len(text) <= 5:
                            # Take a screenshot before clicking to help with debugging
                            page.screenshot(path="before_click_dianzan.png")

                            # Scroll element into view and click
                            element.scroll_into_view_if_needed()
                            element.click()

                            print(f"Clicked '点赞' tab using xpath selector: {selector}")
                            liked_tab_clicked = True
                            time.sleep(5)  # Wait for the liked posts to load

                            # Take a screenshot after clicking to verify
                            page.screenshot(path="after_click_dianzan.png")
                            break
            else:
                elements = page.query_selector_all(selector)
                if elements:
                    for element in elements:
                        text = element.text_content().strip()
                        if text == "点赞" or "点赞" in text and len(text) <= 5:
                            # Take a screenshot before clicking to help with debugging
                            page.screenshot(path="before_click_dianzan.png")

                            # Scroll element into view and click
                            element.scroll_into_view_if_needed()
                            element.click()

                            print(f"Clicked '点赞' tab using selector: {selector}")
                            liked_tab_clicked = True
                            time.sleep(5)  # Wait for the liked posts to load

                            # Take a screenshot after clicking to verify
                            page.screenshot(path="after_click_dianzan.png")
                            break

            if liked_tab_clicked:
                break

        except Exception as e:
            print(f"Failed with selector {selector}: {e}")

    # If we still couldn't click, try a more targeted approach based on the screenshot
    if not liked_tab_clicked:
        try:
            # Take a screenshot to help with debugging
            page.screenshot(path="profile_page.png")

            # Try to directly navigate to the liked posts URL
            print("Trying direct navigation to the liked posts URL...")
            page.goto("https://www.xiaohongshu.com/user/profile?tab=liked", wait_until="domcontentloaded", timeout=30000)
            time.sleep(5)

            # Check if we're on the liked posts page
            if "tab=liked" in page.url:
                print("Successfully navigated to liked posts via direct URL")
                liked_tab_clicked = True
            else:
                # Try to find all elements with text containing "点赞"
                print("Direct navigation didn't work, trying to find all elements with text '点赞'...")
                elements = page.query_selector_all("text=点赞")
                if elements:
                    for element in elements:
                        try:
                            # Get the bounding box to check if it's in the tab area
                            box = element.bounding_box()
                            if box and box['y'] < 300:  # Assuming tabs are at the top of the page
                                element.scroll_into_view_if_needed()
                                element.click()
                                print(f"Clicked '点赞' using visual search")
                                liked_tab_clicked = True
                                time.sleep(5)
                                break
                        except Exception as e:
                            print(f"Failed to click element: {e}")
        except Exception as e:
            print(f"Failed with targeted approach: {e}")

    # If we successfully clicked the "点赞" tab
    if liked_tab_clicked:
        print("Successfully navigated to liked posts via UI")
        return True

    # If UI navigation failed, try direct URL navigation as a fallback
    print("UI navigation failed, trying direct URL navigation...")

    # Try to get the current profile URL first (which should contain the user ID)
    current_url = page.url

    # Check if we're already on a profile page
    if "xiaohongshu.com/user/profile" in current_url:
        # Extract the user ID if present in the URL
        import re
        user_id_match = re.search(r'/user/profile/([^/?]+)', current_url)

        if user_id_match:
            # We have a user ID in the URL
            user_id = user_id_match.group(1)
            liked_url = f"https://www.xiaohongshu.com/user/profile/{user_id}?tab=liked"
        else:
            # No user ID in URL, just add tab parameter to current URL
            if "?" in current_url:
                liked_url = current_url + "&tab=liked"
            else:
                liked_url = current_url + "?tab=liked"

        print(f"Navigating to: {liked_url}")
        page.goto(liked_url, wait_until="domcontentloaded", timeout=30000)
    else:
        # If we're not on a profile page, we need to go back to the main page and try again
        print("Not on profile page, returning to main page to try again...")
        page.goto("https://www.xiaohongshu.com", wait_until="domcontentloaded", timeout=30000)
        time.sleep(3)

        # Try to click the profile link again
        try:
            profile_link = page.query_selector("text=我")
            if profile_link:
                profile_link.click()
                time.sleep(5)

                # Now try to click the liked tab again
                liked_tab = page.query_selector("text=点赞")
                if liked_tab:
                    liked_tab.click()
                    time.sleep(5)
                    liked_tab_clicked = True
        except Exception as e:
            print(f"Failed to navigate via homepage: {e}")

    time.sleep(5)

    # Check if we're on the liked posts page
    if page.query_selector("text=点赞") or "tab=liked" in page.url:
        print("Successfully navigated to liked posts via direct URL")
        return True
    else:
        print("All navigation methods failed")
        return False

def download_image(url, folder, post_id, index):
    """Download an image from URL"""
    if not os.path.exists(folder):
        os.makedirs(folder)

    try:
        # Parse URL to get file extension
        parsed_url = urlparse(url)
        path = parsed_url.path
        ext = os.path.splitext(path)[1]
        if not ext or len(ext) > 5:
            ext = ".jpg"  # Default to jpg if no extension found

        # Create filename
        filename = f"{post_id}_{index}{ext}"
        filepath = os.path.join(folder, filename)

        # Download the image
        response = requests.get(url, stream=True, timeout=10)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print(f"Downloaded image: {filename}")
            return filepath
        else:
            print(f"Failed to download image, status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error downloading image: {e}")
        return None

def summarize_with_openai(post_data):
    """Use OpenAI to summarize content and generate tags"""
    if not OPENAI_API_KEY:
        print("OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
        return {
            'category': 'Uncategorized',
            'tags': '#xiaohongshu',
            'summary': post_data["content"][:200] + "..." if len(post_data["content"]) > 200 else post_data["content"]
        }

    prompt = f"""
    Please analyze this Xiaohongshu post and provide:
    1. A category (single word or short phrase)
    2. 3-5 relevant tags (with # prefix)
    3. A concise summary (under 100 words)

    Post title: {post_data['title']}
    Post content: {post_data['content']}
    Top comments: {post_data['comments']}

    Return your analysis as a JSON object with keys: "category", "tags", "summary"
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            response_format={"type": "json_object"},
            temperature=0.7,
            max_tokens=500
        )

        result_text = response.choices[0].message.content
        result = json.loads(result_text)

        print(f"OpenAI analysis complete:")
        print(f"Category: {result['category']}")
        print(f"Tags: {result['tags']}")
        print(f"Summary: {result['summary'][:100]}...")

        return result

    except Exception as e:
        print(f"Error analyzing with OpenAI: {e}")
        return {
            'category': 'Uncategorized',
            'tags': '#xiaohongshu',
            'summary': post_data["content"][:200] + "..." if len(post_data["content"]) > 200 else post_data["content"]
        }

def send_to_notion(post_data):
    """Send the post data to Notion database matching the updated table structure"""
    try:
        # Check if Notion API credentials are available
        notion_token = os.environ.get("NOTION_TOKEN")
        notion_database_id = os.environ.get("NOTION_DATABASE_ID")

        if not notion_token or not notion_database_id:
            print("Notion API credentials not found. Please set NOTION_TOKEN and NOTION_DATABASE_ID environment variables.")
            return False

        # Initialize Notion client
        from notion_client import Client
        notion = Client(auth=notion_token)

        # Get or generate summary and tags with OpenAI
        if 'ai_analysis' not in post_data:
            print("Generating summary and tags with OpenAI...")
            post_data['ai_analysis'] = summarize_with_openai(post_data)

        # Create properties for Notion page based on the table structure
        properties = {
            "Title": {"title": [{"text": {"content": post_data["title"]}}]},
            "Original Link": {"url": post_data["url"]},
            "Summary": {"rich_text": [{"text": {"content": post_data['ai_analysis']['summary']}}]},
            "Category": {"select": {"name": post_data['ai_analysis']['category']}},
            "Tags": {"multi_select": [{"name": tag.strip("#")} for tag in post_data['ai_analysis']['tags'].split()]}
        }

        # Add image properties if available
        image_urls = post_data["image_urls"]

        # Add 1st Image
        if len(image_urls) >= 1:
            properties["1st Image"] = {
                "files": [
                    {
                        "name": "Image 1",
                        "type": "external",
                        "external": {
                            "url": image_urls[0]
                        }
                    }
                ]
            }

        # Add 2nd Image
        if len(image_urls) >= 2:
            properties["2nd Image"] = {
                "files": [
                    {
                        "name": "Image 2",
                        "type": "external",
                        "external": {
                            "url": image_urls[1]
                        }
                    }
                ]
            }

        # Add 3rd Image
        if len(image_urls) >= 3:
            properties["3rd Image"] = {
                "files": [
                    {
                        "name": "Image 3",
                        "type": "external",
                        "external": {
                            "url": image_urls[2]
                        }
                    }
                ]
            }

        # Create the page in Notion
        notion.pages.create(
            parent={"database_id": notion_database_id},
            properties=properties
        )

        print(f"Successfully sent post '{post_data['title']}' to Notion with {min(len(image_urls), 3)} images")
        return True

    except Exception as e:
        print(f"Error sending to Notion: {e}")
        return False

def extract_post_content(context, post_element=None, url=None):
    """
    Extract content from a single post

    Args:
        context: Browser context
        post_element: Optional post element to click (for liked posts page)
        url: Optional direct URL to navigate to (for single post extraction)
    """
    try:
        print("\nProcessing post...")

        # Get the post page - either by clicking an element or navigating to URL
        if post_element:
            # Check if we should expect a popup or a new tab
            # First, check if there's a popup container already in the page
            page = post_element.page
            popup_container = page.query_selector("div[class*='modal'], div[class*='popup'], div[class*='dialog']")

            if popup_container:
                print("Popup container already exists, clicking post may update it")
                # Store the current URL before clicking
                original_url = page.url

                # Click the post
                post_element.click()
                time.sleep(3)

                # Check if URL changed (popup with URL update)
                current_url = page.url
                if current_url != original_url and "xiaohongshu.com/explore/" in current_url:
                    print(f"URL changed to post URL: {current_url}")
                    post_page = page
                else:
                    print("URL didn't change, but popup may have updated")
                    post_page = page
            else:
                # Try to open in a new tab
                try:
                    with context.expect_page() as new_page_info:
                        post_element.click()
                        time.sleep(3)

                    # Get the new page
                    post_page = new_page_info.value
                    post_page.wait_for_load_state("domcontentloaded", timeout=30000)
                    print("Post opened in a new tab")
                except Exception as e:
                    print(f"Failed to open in new tab: {e}")
                    print("Assuming post opened in the same page")
                    post_page = post_element.page
                    # Take a screenshot to see what happened
                    post_page.screenshot(path="same_page_post.png")
        elif url:
            # Check if the URL contains "xiaohongshu.com/explore/" which indicates a post
            if "xiaohongshu.com/explore/" in url:
                print("Valid Xiaohongshu post URL detected")
            else:
                print("Warning: URL may not be a valid Xiaohongshu post URL")

            # Create a new page and navigate to the URL
            post_page = context.new_page()
            print(f"Navigating to post URL: {url}")
            post_page.goto(url, wait_until="domcontentloaded", timeout=30000)
            time.sleep(5)
        else:
            raise ValueError("Either post_element or url must be provided")

        # Get post URL
        url = post_page.url
        print(f"Post URL: {url}")

        # Extract post ID from URL - handle new URL format with query parameters
        if "?" in url:
            # Format: https://www.xiaohongshu.com/explore/680e0d91000000002301281e?xsec_token=...
            post_id = url.split("/")[-1].split("?")[0]
        else:
            # Old format without query parameters
            post_id = url.split("/")[-1]

        print(f"Extracted post ID: {post_id}")

        # Check if we're in a popup window
        popup_container = post_page.query_selector("div[class*='modal'], div[class*='popup'], div[class*='dialog']")
        if popup_container:
            print("Detected popup container - will focus extraction there")
            # Take a screenshot of the popup
            post_page.screenshot(path="popup_container.png")
            print("Screenshot saved as popup_container.png")

        # Extract title - updated selectors based on the latest HTML structure
        title_selectors = [
            # Popup-specific selectors based on your screenshot
            "div[class*='modal'] h1",
            "div[class*='modal'] div[class*='title']",
            "div[class*='popup'] h1",
            "div[class*='popup'] div[class*='title']",
            "div[class*='dialog'] h1",
            "div[class*='dialog'] div[class*='title']",
            # Based on the screenshot HTML structure
            "h1.title",
            "div.title",
            "div.content > div:first-child",
            "div.user-info + div",
            "div.note-content h1",
            "div.note-detail h1",
            "div[class*='title']",
            # More specific selectors based on the screenshot
            "div[class*='note-detail'] h1",
            "div[class*='note-content'] h1",
            "div[class*='content'] h1",
            "div[class*='header'] h1",
            # Fallback to meta tags
            "meta[property='og:title']"
        ]

        title = "Xiaohongshu Post"
        for selector in title_selectors:
            try:
                # First try within popup container if it exists
                if popup_container:
                    title_element = popup_container.query_selector(selector)
                    if title_element:
                        if selector.startswith("meta"):
                            extracted_title = title_element.get_attribute("content")
                        else:
                            extracted_title = title_element.text_content()

                        if extracted_title and len(extracted_title.strip()) > 0:
                            title = extracted_title.strip()
                            print(f"Found title in popup using selector: {selector}")
                            break

                # If not found in popup or no popup exists, try the full page
                title_element = post_page.query_selector(selector)
                if title_element:
                    if selector.startswith("meta"):
                        extracted_title = title_element.get_attribute("content")
                    else:
                        extracted_title = title_element.text_content()

                    if extracted_title and len(extracted_title.strip()) > 0:
                        title = extracted_title.strip()
                        print(f"Found title using selector: {selector}")
                        break
            except Exception as e:
                print(f"Error with title selector {selector}: {e}")

        # Extract content - updated selectors based on the latest HTML structure
        content_selectors = [
            # Popup-specific selectors based on your screenshot
            "div[class*='modal'] div[class*='content']",
            "div[class*='modal'] div[class*='desc']",
            "div[class*='popup'] div[class*='content']",
            "div[class*='popup'] div[class*='desc']",
            "div[class*='dialog'] div[class*='content']",
            "div[class*='dialog'] div[class*='desc']",
            # Original selectors
            "div.content",
            "div.desc",
            "div.note-content",
            "article",
            # More specific selectors based on the screenshot
            "div[class*='note-detail'] div[class*='content']",
            "div[class*='note-content'] div[class*='desc']",
            "div[class*='content-container']",
            "div[class*='note-container'] div[class*='content']",
            # Fallback selectors
            "div[class*='content']",
            "div.user-info + div + div",
            "div.note-detail div[class*='content']"
        ]

        content = ""
        for selector in content_selectors:
            try:
                # First try within popup container if it exists
                if popup_container:
                    content_element = popup_container.query_selector(selector)
                    if content_element:
                        extracted_content = content_element.text_content().strip()
                        if extracted_content and len(extracted_content) > len(content):
                            content = extracted_content
                            print(f"Found content in popup using selector: {selector}")
                            if len(content) > 50:  # If we found substantial content, break early
                                break

                # If not found in popup or no popup exists, try the full page
                content_element = post_page.query_selector(selector)
                if content_element:
                    extracted_content = content_element.text_content().strip()
                    if extracted_content and len(extracted_content) > len(content):
                        content = extracted_content
                        print(f"Found content using selector: {selector}")
                        if len(content) > 50:  # If we found substantial content, break early
                            break
            except Exception as e:
                print(f"Error with content selector {selector}: {e}")

        if not content:
            # Try JavaScript approach to extract content
            try:
                js_content = post_page.evaluate("""
                    (popupSelector) => {
                        // First check if we have a popup
                        const popup = popupSelector ? document.querySelector(popupSelector) : null;
                        const rootElement = popup || document;

                        // Look for content elements
                        const contentElements = rootElement.querySelectorAll('[class*="content" i], [class*="desc" i], article, p');
                        let longestContent = '';

                        for (const el of contentElements) {
                            const text = el.innerText.trim();
                            // Skip very short texts or elements that look like titles
                            if (text.length > 20 && text.length > longestContent.length &&
                                !el.tagName.toLowerCase().startsWith('h') &&
                                window.getComputedStyle(el).fontSize < '20px') {
                                longestContent = text;
                            }
                        }

                        return longestContent;
                    }
                """, popup_container and "div[class*='modal'], div[class*='popup'], div[class*='dialog']")

                if js_content and len(js_content) > 0:
                    content = js_content
                    print("Found content using JavaScript approach")
            except Exception as e:
                print(f"Error with JavaScript content extraction: {e}")

        if not content:
            content = "Content could not be extracted"

        # Extract images with improved selectors based on the screenshot
        image_urls = []
        downloaded_images = []

        print("Extracting images using multiple methods...")

        # Method 1: Try to find image URLs directly from the HTML structure
        # Based on the screenshot, images are often in img tags with specific src patterns
        try:
            # First attempt: Look for image URLs in the HTML source
            content = post_page.content()

            # Look for image URLs in the HTML source using regex patterns from the screenshot
            img_patterns = [
                # Patterns from the screenshot you provided
                r'https://ci\.xiaohongshu\.com[^"\']+\.jpg',
                r'https://ci\.xiaohongshu\.com[^"\']+\.png',
                r'https://ci\.xiaohongshu\.com[^"\']+\.webp',
                # Additional patterns for other image domains
                r'https://sns-img-bd\.xhscdn\.com[^"\']+',
                r'https://sns-img-qc\.xhscdn\.com[^"\']+',
                r'https://sns-img\.xhscdn\.com[^"\']+',
                r'https://sns-img-hw\.xhscdn\.com[^"\']+',
                r'https://xhscdn\.com[^"\']+\.(jpg|png|webp)',
                # More specific patterns for popup window images
                r'https://ci\.xiaohongshu\.com[^"\']+/format/[^"\']+',
                r'https://ci\.xiaohongshu\.com[^"\']+/resize/[^"\']+',
                r'https://sns-img[^"\']+\.xhscdn\.com[^"\']+/format/[^"\']+',
                r'https://sns-img[^"\']+\.xhscdn\.com[^"\']+/resize/[^"\']+',
                # Generic pattern for any image URL
                r'https?://[^"\']+\.(jpg|jpeg|png|webp|gif)'
            ]

            for pattern in img_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if isinstance(match, tuple):  # If the regex has groups
                        match = match[0]
                    if match not in image_urls and not ("avatar" in match or "icon" in match):
                        image_urls.append(match)
                        print(f"Found image URL using regex pattern: {match[:50]}...")
                        if len(image_urls) >= 3:
                            break
                if len(image_urls) >= 3:
                    break

            if image_urls:
                print(f"Found {len(image_urls)} image URLs using regex patterns")
        except Exception as e:
            print(f"Error extracting images from HTML source: {e}")

        # Method 2: If we didn't find enough images with regex, try using selectors
        if len(image_urls) < 3:
            print("Trying to find images using selectors...")

            # Updated image selectors based on the screenshot
            image_selectors = [
                # Based on the screenshot HTML structure
                "img[src*='ci.xiaohongshu.com']",
                "img[src*='sns-img']",
                "img[src*='xhscdn.com']",
                # Original selectors
                "img[class*='note']",
                "img[src*='xiaohongshu']",
                "div[class*='image'] img",
                ".media-container img",
                ".swiper-slide img",
                ".carousel-item img"
            ]

            for selector in image_selectors:
                try:
                    img_elements = post_page.query_selector_all(selector)
                    for img in img_elements:
                        if len(image_urls) >= 3:
                            break

                        src = img.get_attribute("src")
                        if src and not ("avatar" in src or "icon" in src):
                            # Only include larger images (likely content images, not UI elements)
                            width = img.get_attribute("width")
                            height = img.get_attribute("height")

                            try:
                                width = int(width) if width else 0
                                height = int(height) if height else 0
                            except:
                                width = height = 0

                            if width == 0 or height == 0 or (width > 100 and height > 100):
                                if src not in image_urls:
                                    image_urls.append(src)
                                    print(f"Found image using selector {selector}: {src[:50]}...")
                except Exception as e:
                    print(f"Error with image selector {selector}: {e}")

                if len(image_urls) >= 3:
                    break

        # Method 3: If we still don't have enough images, try the slider navigation approach
        if len(image_urls) < 3:
            print("Trying slider navigation approach...")

            # Check if there's a slider container
            slider_container = post_page.query_selector("div[class*='slider'], div[class*='carousel'], div[class*='swiper'], div.media-container")
            has_slider = slider_container is not None

            if has_slider:
                print("Found image slider, navigating through images...")

                # Find the right arrow button
                arrow_selectors = [
                    "button[class*='arrow'][class*='right'], button[class*='next']",
                    "div[class*='arrow'][class*='right'], div[class*='next']",
                    "span[class*='arrow'][class*='right'], span[class*='next']",
                    "svg[class*='arrow'][class*='right'], svg[class*='next']",
                    "[aria-label='Next'], [aria-label='下一张']",
                    ".media-container .right, .media-container .next"
                ]

                right_arrow = None
                for selector in arrow_selectors:
                    try:
                        right_arrow = post_page.query_selector(selector)
                        if right_arrow:
                            print(f"Found right arrow using selector: {selector}")
                            break
                    except Exception as e:
                        print(f"Error with arrow selector {selector}: {e}")

                if right_arrow:
                    # Click through the slider to get more images
                    max_clicks = 5  # Try more clicks to find images
                    for i in range(max_clicks):
                        if len(image_urls) >= 3:
                            break  # Stop if we already have 3 images

                        try:
                            # Click the right arrow
                            right_arrow.click()
                            time.sleep(1)  # Wait for image to load

                            # After clicking, check for new images in the page content
                            content = post_page.content()
                            for pattern in img_patterns:
                                matches = re.findall(pattern, content)
                                for match in matches:
                                    if isinstance(match, tuple):  # If the regex has groups
                                        match = match[0]
                                    if match not in image_urls and not ("avatar" in match or "icon" in match):
                                        image_urls.append(match)
                                        print(f"Found image URL after slider click: {match[:50]}...")
                                        if len(image_urls) >= 3:
                                            break
                                if len(image_urls) >= 3:
                                    break
                        except Exception as e:
                            print(f"Error navigating to next image: {e}")
                            break

        # If we still don't have any images, try one last approach with JavaScript
        if len(image_urls) == 0:
            print("Trying JavaScript approach to extract images...")
            try:
                # Use JavaScript to find all image elements and their sources
                js_image_urls = post_page.evaluate("""
                    Array.from(document.querySelectorAll('img'))
                        .map(img => img.src)
                        .filter(src => src &&
                            (src.includes('xiaohongshu') ||
                             src.includes('xhscdn') ||
                             src.includes('sns-img')) &&
                            !src.includes('avatar') &&
                            !src.includes('icon'))
                """)

                for url in js_image_urls:
                    if url not in image_urls:
                        image_urls.append(url)
                        if len(image_urls) >= 3:
                            break

                print(f"Found {len(image_urls)} images using JavaScript approach")
            except Exception as e:
                print(f"Error with JavaScript image extraction: {e}")

        print(f"Total images found: {len(image_urls)}")

        # Download the first 3 images
        for i, img_url in enumerate(image_urls[:3]):
            img_path = download_image(img_url, IMAGE_FOLDER, post_id, i+1)
            if img_path:
                downloaded_images.append(img_path)

        # Extract comments
        comments = []

        # First try to find comments in the popup if it exists
        if popup_container:
            comment_elements = popup_container.query_selector_all("div[class*='comment-item'], div[class*='comment'], div[class*='reply']")
            for comment in comment_elements[:3]:  # Get top 3 comments
                comment_text = comment.text_content().strip()
                if comment_text and len(comment_text) > 5:  # Avoid empty or very short comments
                    comments.append(comment_text)
                    print(f"Found comment in popup: {comment_text[:30]}...")

        # If no comments found in popup or no popup exists, try the full page
        if len(comments) == 0:
            comment_elements = post_page.query_selector_all("div[class*='comment-item'], div[class*='comment'], div[class*='reply']")
            for comment in comment_elements[:3]:  # Get top 3 comments
                comment_text = comment.text_content().strip()
                if comment_text and len(comment_text) > 5:  # Avoid empty or very short comments
                    comments.append(comment_text)
                    print(f"Found comment: {comment_text[:30]}...")

        # If still no comments, try JavaScript approach
        if len(comments) == 0:
            try:
                js_comments = post_page.evaluate("""
                    (popupSelector) => {
                        // First check if we have a popup
                        const popup = popupSelector ? document.querySelector(popupSelector) : null;
                        const rootElement = popup || document;

                        // Look for comment elements
                        const commentElements = rootElement.querySelectorAll('[class*="comment" i], [class*="reply" i]');
                        const comments = [];

                        for (const el of commentElements) {
                            const text = el.innerText.trim();
                            if (text.length > 5 && !comments.includes(text)) {
                                comments.push(text);
                                if (comments.length >= 3) break;
                            }
                        }

                        return comments;
                    }
                """, popup_container and "div[class*='modal'], div[class*='popup'], div[class*='dialog']")

                if js_comments and len(js_comments) > 0:
                    for comment in js_comments:
                        if comment not in comments:
                            comments.append(comment)
                    print(f"Found {len(js_comments)} comments using JavaScript approach")
            except Exception as e:
                print(f"Error with JavaScript comment extraction: {e}")

        # Create post data
        post_data = {
            "id": post_id,
            "title": title,
            "url": url,
            "content": content,
            "image_urls": image_urls[:3],  # Limit to first 3 images
            "downloaded_images": downloaded_images,
            "comments": comments,
            "extracted_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        print(f"Successfully extracted post: {title}")
        print(f"Content length: {len(content)} characters")
        print(f"Images found: {len(image_urls[:3])}")
        print(f"Comments: {len(comments)}")

        # Generate summary and tags with OpenAI
        print("\nGenerating summary and tags with OpenAI...")
        post_data['ai_analysis'] = summarize_with_openai(post_data)

        # Send to Notion immediately
        print("\nSending to Notion...")
        send_to_notion(post_data)

        # Close the post page
        post_page.close()

        return post_data

    except Exception as e:
        print(f"Error extracting post content: {e}")
        # Close any extra pages
        for page in context.pages[1:]:
            page.close()
        return None

def main():
    """Main function to extract liked posts"""
    # Create image folder if downloading images
    if DOWNLOAD_IMAGES and not os.path.exists(IMAGE_FOLDER):
        os.makedirs(IMAGE_FOLDER)

    # Check if URL is provided as command line argument
    if len(sys.argv) > 1 and "xiaohongshu.com" in sys.argv[1]:
        # Extract single post from URL
        url = sys.argv[1]

        # Setup browser and extract post
        playwright, browser, context = setup_browser()
        try:
            post_data = extract_post_content(context, url=url)
            if post_data:
                print("\n--- Post Extraction Summary ---")
                print(f"Title: {post_data['title']}")
                print(f"URL: {post_data['url']}")
                print(f"Content length: {len(post_data['content'])} characters")
                print(f"Images: {len(post_data['image_urls'])}")
                print(f"Downloaded images: {len(post_data['downloaded_images'])}")
                print(f"Comments: {len(post_data['comments'])}")
        finally:
            browser.close()
            playwright.stop()
        return

    # If no URL provided, proceed with liked posts extraction
    playwright, browser, context = setup_browser()

    try:
        # Create a new page
        page = context.new_page()

        # Login to Xiaohongshu
        if not login_to_xiaohongshu(page):
            print("Login failed. Exiting...")
            input("Press Enter to close the browser...")
            return

        print("Login successful! Continuing with extraction...")

        # Navigate to liked posts (directly click 我 then 点赞)
        if not navigate_to_liked_posts(page):
            print("Failed to navigate to liked posts. Exiting...")
            input("Press Enter to close the browser...")
            return

        # Find post elements
        print("Finding post elements...")
        time.sleep(5)

        post_elements = page.query_selector_all("div[class*='note-item'], div[class*='feed-item'], section[class*='note'], div[class*='note']")

        if not post_elements or len(post_elements) == 0:
            print("No post elements found with primary selector, trying alternative...")
            post_elements = page.query_selector_all("div[role='listitem'], div[class*='item']")

        post_count = len(post_elements)
        print(f"Found {post_count} post elements")

        if post_count == 0:
            print("No posts found. Exiting...")
            input("Press Enter to close the browser...")
            return

        # Process only the first post
        print("\nClicking on the first post to extract its content...")
        if post_count > 0:
            first_post = post_elements[0]
            print("Clicking on the first post...")

            # Click on the post - it might open in a popup or new tab
            try:
                # First, get the current URL before clicking
                original_url = page.url
                print(f"Original URL before clicking: {original_url}")

                # Click the post
                first_post.click()
                print("Clicked on the first post")
                time.sleep(5)  # Wait longer for popup or new page to load

                # Check if URL changed in the current page (popup scenario)
                current_url = page.url
                print(f"Current URL after clicking: {current_url}")

                # Take a screenshot of the current state
                page.screenshot(path="after_click.png")
                print("Screenshot saved as after_click.png")

                post_page = None
                post_url = None

                # Check if we're still on the same URL (which means it might be a popup within the same page)
                if current_url != original_url and "xiaohongshu.com/explore/" in current_url:
                    print("URL changed in the current page - likely a popup post view")
                    post_page = page
                    post_url = current_url
                else:
                    # Check if a new tab was opened
                    print("Checking if a new tab was opened...")
                    if len(context.pages) > 1:
                        # Get the most recently opened page
                        post_page = context.pages[-1]
                        post_url = post_page.url
                        print(f"New tab detected with URL: {post_url}")
                    else:
                        # If no new tab and URL didn't change, try to find a popup within the page
                        print("No new tab detected, checking for popup...")
                        # Look for popup container
                        popup = page.query_selector("div[class*='modal'], div[class*='popup'], div[class*='dialog']")
                        if popup:
                            print("Found popup container in the page")
                            post_page = page
                            post_url = current_url
                        else:
                            print("No popup found, using current page")
                            post_page = page
                            post_url = current_url

                # Print the final URL we'll use
                print(f"\n🔗 Post URL: {post_url}")

                # Take a screenshot of the post page
                if post_page:
                    post_page.screenshot(path="post_page.png")
                    print("Screenshot saved as post_page.png")

                # Now extract the content
                print("\nExtracting content from the post...")

                # If we have a valid post URL that contains "explore", use it directly
                if post_url and "xiaohongshu.com/explore/" in post_url:
                    post_data = extract_post_content(context, url=post_url)
                else:
                    # Otherwise try to extract from the current state
                    post_data = extract_post_content(context, post_element=first_post)

                if post_data:
                    print(f"✅ Successfully processed the first post")
                    print(f"\n--- Post Details ---")
                    print(f"Title: {post_data['title']}")
                    print(f"Content length: {len(post_data['content'])} characters")
                    print(f"Images found: {len(post_data['image_urls'])}")
                    print(f"Downloaded images: {len(post_data['downloaded_images'])}")
                else:
                    print(f"❌ Failed to process the first post")

                # If we opened a new tab, close it
                if post_page and post_page != page:
                    post_page.close()

            except Exception as e:
                print(f"Error clicking on the first post: {e}")
                print("Trying alternative method...")

                # Try alternative method
                post_data = extract_post_content(context, post_element=first_post)
                if post_data:
                    print(f"✅ Successfully processed the first post using alternative method")
                    print(f"\n--- Post Details ---")
                    print(f"Title: {post_data['title']}")
                    print(f"URL: {post_data['url']}")
                    print(f"Content length: {len(post_data['content'])} characters")
                    print(f"Images found: {len(post_data['image_urls'])}")
                else:
                    print(f"❌ Failed to process the first post")
        else:
            print("No posts found to process")

        # Return to the liked posts page
        page.goto("https://www.xiaohongshu.com/user/profile?tab=liked", wait_until="domcontentloaded", timeout=30000)

        # Take a final screenshot and close the browser
        print("\nTaking a final screenshot and closing the browser...")
        page.screenshot(path="final_state.png")
        time.sleep(3)

    except Exception as e:
        print(f"An error occurred: {e}")
        input("Press Enter to close the browser...")

    finally:
        browser.close()
        playwright.stop()

if __name__ == "__main__":
    main()

