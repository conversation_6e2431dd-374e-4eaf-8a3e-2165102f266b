import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from openai import OpenAI
from notion_client import Client
import json
from dotenv import load_dotenv

# Configuration - Load from environment variables
load_dotenv()

OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_ID = os.environ.get("NOTION_DATABASE_ID")
XIAOHONGSHU_USERNAME = os.environ.get("XIAOHONGSHU_USERNAME", "your_username")
XIAOHONGSHU_PASSWORD = os.environ.get("XIAOHONGSHU_PASSWORD", "your_password")

# Initialize clients
openai_client = OpenAI(api_key=OPENAI_API_KEY)
notion = Client(auth=NOTION_TOKEN)

def setup_driver():
    """Set up and return a configured Chrome WebDriver"""
    chrome_options = Options()
    # Uncomment the line below if you want to run headless
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def check_login_status(driver):
    """Check if already logged in to Xiaohongshu"""
    driver.get("https://www.xiaohongshu.com/user/profile")
    time.sleep(3)
    
    # Check if we're on the login page or already logged in
    try:
        # Look for elements that would only appear when logged in
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'user-avatar') or contains(@class, 'profile-info')]"))
        )
        print("Already logged in to Xiaohongshu")
        return True
    except:
        print("Not logged in to Xiaohongshu")
        return False

def login_to_xiaohongshu(driver):
    """Login to Xiaohongshu account if not already logged in"""
    # First check if already logged in
    if check_login_status(driver):
        return
    
    # If not logged in, proceed with login
    driver.get("https://www.xiaohongshu.com/")
    print("Navigating to Xiaohongshu...")
    
    # Wait for login button to appear and click it
    try:
        login_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '登录')]"))
        )
        login_button.click()
        print("Clicked login button")
        
        # Wait for QR code to appear
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'qrcode') or contains(@class, 'login-qrcode')]"))
        )
        
        print("Please scan the QR code with your phone...")
        
        # Wait for user to scan QR code and complete login
        input("Press Enter after you've completed the login process...")
        
        # Verify login was successful
        if check_login_status(driver):
            print("Login successful")
        else:
            print("Login failed. Please try again.")
            
    except Exception as e:
        print(f"Login process encountered an error: {e}")
        input("Please complete the login manually and press Enter to continue...")

def navigate_to_liked_posts(driver):
    """Navigate to the liked posts page"""
    # Navigate to user profile
    driver.get("https://www.xiaohongshu.com/user/profile")
    print("Navigating to user profile...")
    time.sleep(3)
    
    # Click on liked posts tab
    try:
        liked_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '点赞')]"))
        )
        liked_tab.click()
        print("Navigated to liked posts")
        time.sleep(3)
    except Exception as e:
        print(f"Failed to navigate to liked posts: {e}")
        # Alternative: directly navigate to liked posts URL
        driver.get("https://www.xiaohongshu.com/user/profile?tab=liked")
        time.sleep(3)

def get_liked_posts(driver, limit=2):
    """Extract information from liked posts"""
    posts = []
    
    try:
        # Find all post elements
        post_elements = WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'note-item')]"))
        )
        
        for i, post in enumerate(post_elements[:limit]):
            try:
                # Extract title
                title = post.find_element(By.XPATH, ".//div[contains(@class, 'title')]").text
                
                # Extract URL by clicking on the post and getting the URL
                post.click()
                time.sleep(2)
                
                # Switch to the new tab
                driver.switch_to.window(driver.window_handles[-1])
                url = driver.current_url
                
                # Extract content
                content_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'content')]"))
                )
                content = content_element.text
                
                # Extract top comments
                comments = []
                try:
                    comment_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'comment-item')]")
                    for comment in comment_elements[:3]:  # Get top 3 comments
                        comments.append(comment.text)
                except:
                    pass
                
                posts.append({
                    "title": title,
                    "url": url,
                    "content": content,
                    "comments": comments
                })
                
                # Close the tab and switch back
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                time.sleep(1)
                
            except Exception as e:
                print(f"Error processing post {i+1}: {e}")
                # If a tab was opened, close it and go back
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                
    except Exception as e:
        print(f"Failed to extract liked posts: {e}")
    
    return posts

def analyze_with_openai(post):
    """Use OpenAI to summarize content and generate tags"""
    prompt = f"""
    Please analyze this Xiaohongshu post and provide:
    1. A category (single word or short phrase)
    2. 3-5 relevant tags (with # prefix)
    3. A concise summary (under 100 words)
    
    Post title: {post['title']}
    Post content: {post['content']}
    Top comments: {post['comments']}
    
    Return your analysis as a Python dictionary with keys: 'category', 'tags', 'summary'
    """
    
    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7,
            max_tokens=500
        )
        
        # Extract and parse the response
        result_text = response.choices[0].message.content
        
        # Try to parse as JSON/dict
        try:
            # First try direct eval (safer than eval)
            import ast
            result = ast.literal_eval(result_text)
        except:
            # If that fails, try to extract dictionary-like text and eval that
            import re
            dict_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if dict_match:
                result = ast.literal_eval(dict_match.group(0))
            else:
                # Fallback to manual parsing
                lines = result_text.split('\n')
                result = {
                    'category': next((line.split(':', 1)[1].strip() for line in lines if 'category' in line.lower()), ''),
                    'tags': next((line.split(':', 1)[1].strip() for line in lines if 'tags' in line.lower()), ''),
                    'summary': next((line.split(':', 1)[1].strip() for line in lines if 'summary' in line.lower()), '')
                }
        
        return result
    
    except Exception as e:
        print(f"Error analyzing with OpenAI: {e}")
        return {
            'category': 'Uncategorized',
            'tags': '#uncategorized',
            'summary': 'Failed to generate summary.'
        }

def add_to_notion(post_data):
    """Add the analyzed post to Notion database"""
    try:
        properties = {
            "标题": {"title": [{"text": {"content": post_data["title"]}}]},
            "分类": {"select": {"name": post_data["category"]}},
            "标签": {"multi_select": [{"name": tag.strip("#")} for tag in post_data["tags"].split()]},
            "总结": {"rich_text": [{"text": {"content": post_data["summary"]}}]},
            "原文链接": {"url": post_data["url"]}
        }
        
        notion.pages.create(
            parent={"database_id": NOTION_DATABASE_ID},
            properties=properties
        )
        
        print(f"Successfully added to Notion: {post_data['title']}")
        return True
    
    except Exception as e:
        print(f"Error adding to Notion: {e}")
        return False

def main():
    """Main function to run the script"""
    driver = setup_driver()
    
    try:
        # Check if already logged in, and login if needed
        login_to_xiaohongshu(driver)
        
        # Navigate to liked posts
        navigate_to_liked_posts(driver)
        
        # Get liked posts
        posts = get_liked_posts(driver, limit=5)
        print(f"Found {len(posts)} posts")
        
        # Process each post
        for i, post in enumerate(posts):
            print(f"\nProcessing post {i+1}/{len(posts)}: {post['title']}")
            
            # Analyze with OpenAI
            analysis = analyze_with_openai(post)
            
            # Prepare data for Notion
            post_data = {
                "title": post["title"],
                "url": post["url"],
                "category": analysis["category"],
                "tags": analysis["tags"],
                "summary": analysis["summary"]
            }
            
            # Add to Notion
            success = add_to_notion(post_data)
            if success:
                print(f"✅ Post {i+1}/{len(posts)} successfully added to Notion")
            else:
                print(f"❌ Failed to add post {i+1}/{len(posts)} to Notion")
            
            # Small delay between processing posts
            time.sleep(2)
            
    except Exception as e:
        print(f"An error occurred in the main process: {e}")
    
    finally:
        print("Closing browser...")
        driver.quit()

if __name__ == "__main__":
    main()




