import os
import time
import json
from playwright.sync_api import sync_playwright, TimeoutError
from dotenv import load_dotenv
from openai import OpenAI
from notion_client import Client

# Load environment variables
load_dotenv()

# Configuration
USER_ID = os.environ.get("XIAOHONGSHU_USER_ID", "56c02c48b8c8b42457b02064")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_ID = os.environ.get("NOTION_DATABASE_ID")

# Initialize clients
openai_client = OpenAI(api_key=OPENAI_API_KEY)
notion = Client(auth=NOTION_TOKEN)

def summarize_with_openai(post):
    """Use OpenAI to summarize content and generate tags"""
    prompt = f"""
    Please analyze this Xiaohongshu post and provide:
    1. A category (single word or short phrase)
    2. 3-5 relevant tags (with # prefix)
    3. A concise summary (under 100 words)
    
    Post title: {post['title']}
    Post content: {post['content']}
    Top comments: {post['comments']}
    
    Return your analysis as a JSON object with keys: "category", "tags", "summary"
    """
    
    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            response_format={"type": "json_object"},
            temperature=0.7,
            max_tokens=500
        )
        
        result_text = response.choices[0].message.content
        result = json.loads(result_text)
        
        return result
    
    except Exception as e:
        print(f"Error analyzing with OpenAI: {e}")
        return {
            'category': 'Uncategorized',
            'tags': '#uncategorized',
            'summary': 'Failed to generate summary.'
        }

def add_to_notion(post_data):
    """Add the analyzed post to Notion database"""
    try:
        properties = {
            "标题": {"title": [{"text": {"content": post_data["title"]}}]},
            "分类": {"select": {"name": post_data["category"]}},
            "标签": {"multi_select": [{"name": tag.strip("#")} for tag in post_data["tags"].split()]},
            "总结": {"rich_text": [{"text": {"content": post_data["summary"]}}]},
            "原文链接": {"url": post_data["url"]}
        }
        
        notion.pages.create(
            parent={"database_id": NOTION_DATABASE_ID},
            properties=properties
        )
        
        print(f"Successfully added to Notion: {post_data['title']}")
        return True
    
    except Exception as e:
        print(f"Error adding to Notion: {e}")
        return False

def run_test():
    """Run the test using Playwright"""
    with sync_playwright() as p:
        # Launch the browser with stealth mode
        browser = p.chromium.launch(
            headless=False,
            args=[
                "--window-size=1920,1080",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=IsolateOrigins,site-per-process",
                "--no-sandbox",
                "--disable-setuid-sandbox"
            ]
        )
        
        # Create a context with viewport and user agent
        context = browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            ignore_https_errors=True
        )
        
        # Set default timeout for all operations
        context.set_default_timeout(60000)
        
        # Add script to bypass detection
        context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en', 'zh-CN'],
            });
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32'
            });
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8
            });
        """)
        
        # Create a new page
        page = context.new_page()
        
        try:
            # Navigate to homepage
            print("Navigating to Xiaohongshu homepage...")
            try:
                page.goto("https://www.xiaohongshu.com/", wait_until="domcontentloaded", timeout=30000)
            except TimeoutError:
                print("Homepage load timed out, but continuing...")
            
            # Check for connection error
            if page.query_selector("text=连接异常") or page.query_selector("text=网络一下试试") or page.query_selector("text=点击刷新"):
                print("Connection error detected. The site is blocking automated access.")
                print("Test stopped due to error page. You may need to:")
                print("1. Use a VPN with a Chinese server")
                print("2. Try at a different time")
                print("3. Use a different network connection")
                return
            
            time.sleep(5)
            
            # Click login button
            print("Clicking login button...")
            login_clicked = False
            selectors = [
                "text=登录",
                "button:has-text('登录')",
                "div:has-text('登录'):not(:has-div)",
                "a:has-text('登录')",
                ".login-button",
                ".sign-in-button"
            ]
            
            for selector in selectors:
                try:
                    if page.query_selector(selector):
                        page.click(selector, timeout=5000)
                        print(f"Clicked login button using selector: {selector}")
                        login_clicked = True
                        break
                except Exception:
                    continue
            
            if not login_clicked:
                print("Could not find login button automatically")
                print("Please login manually when the page loads...")
            
            # Wait for QR code
            try:
                page.wait_for_selector("div[class*='qrcode'], img[class*='qrcode']", timeout=10000)
                print("Please scan the QR code with your phone...")
            except:
                print("QR code not detected automatically")
                print("Please proceed with manual login...")
            
            # Wait for user to complete login
            input("Press Enter after you've completed the login process...")
            time.sleep(5)
            
            # Navigate to liked posts
            print("Navigating to liked posts tab...")
            try:
                liked_posts_url = f"https://www.xiaohongshu.com/user/profile/{USER_ID}?tab=liked"
                print(f"Navigating to: {liked_posts_url}")
                page.goto(liked_posts_url, wait_until="domcontentloaded", timeout=30000)
                print("Navigated to liked posts tab")
            except TimeoutError:
                print("Liked posts page load timed out, but continuing...")
            
            time.sleep(5)
            
            # Check for error page
            if page.query_selector("text=连接异常") or page.query_selector("text=网络一下试试") or page.query_selector("text=点击刷新"):
                print("Connection error detected on liked posts page. The site is blocking automated access.")
                print("Test stopped due to error page.")
                return
            
            # Check for empty liked posts
            empty_state = page.query_selector("text=没有你点赞过的内容, text=暂无点赞, text=还没有点赞内容")
            if empty_state:
                print("No liked posts found - your liked posts tab is empty")
                print("Please like some posts first and then run the script again")
                return
            
            # Find post elements
            print("Finding post elements...")
            time.sleep(5)
            
            post_elements = page.query_selector_all("div[class*='note-item'], div[class*='feed-item'], section[class*='note'], div[class*='note']")
            
            if not post_elements or len(post_elements) == 0:
                print("No post elements found with primary selector, trying alternative...")
                post_elements = page.query_selector_all("div[role='listitem'], div[class*='item']")
            
            print(f"Found {len(post_elements)} post elements")
            
            if len(post_elements) == 0:
                print("No posts found. The page might not be loading correctly.")
                print("Test stopped due to no posts found.")
                return
            
            # Process first post only
            if len(post_elements) > 0:
                post = post_elements[0]
                try:
                    print("Processing first post...")
                    
                    # Click on the post to open it
                    with context.expect_page() as new_page_info:
                        post.click()
                        time.sleep(3)
                    
                    # Get the new page
                    post_page = new_page_info.value
                    post_page.wait_for_load_state("domcontentloaded", timeout=30000)
                    
                    # Extract title from HTML - try multiple selectors
                    print("Extracting title from HTML...")
                    title_selectors = [
                        "h1", 
                        "div[class*='title']", 
                        "div[class*='content'] > div:first-child",
                        "div[class*='note-detail'] h1",
                        "div[class*='note-content'] h1",
                        "meta[property='og:title']"
                    ]
                    
                    title = "Xiaohongshu Post"
                    for selector in title_selectors:
                        title_element = post_page.query_selector(selector)
                        if title_element:
                            if selector.startswith("meta"):
                                # For meta tags, get the content attribute
                                extracted_title = title_element.get_attribute("content")
                            else:
                                # For regular elements, get the text content
                                extracted_title = title_element.text_content()
                            
                            if extracted_title and len(extracted_title.strip()) > 0:
                                title = extracted_title.strip()
                                print(f"Found title using selector: {selector}")
                                break
                    
                    # Get URL by right-clicking on the main image
                    print("Getting post URL...")
                    main_image = post_page.query_selector("img[class*='main'], img[class*='post'], div[class*='main-image'] img, img[class*='note']")
                    if main_image:
                        # Right-click on the image
                        main_image.click(button="right")
                        time.sleep(1)
                        
                        # Click "复制笔记链接" option
                        copy_link_option = post_page.query_selector("text=复制笔记链接")
                        if copy_link_option:
                            copy_link_option.click()
                            time.sleep(1)
                            
                            # Create input field to get clipboard content
                            post_page.evaluate("""() => {
                                const input = document.createElement('input');
                                input.id = 'clipboard-input';
                                document.body.appendChild(input);
                            }""")
                            
                            # Paste and get URL
                            input_field = post_page.query_selector("#clipboard-input")
                            input_field.focus()
                            post_page.keyboard.press("Control+v")
                            time.sleep(1)
                            
                            clipboard_url = post_page.evaluate("document.getElementById('clipboard-input').value")
                            if clipboard_url and "xiaohongshu.com" in clipboard_url:
                                url = clipboard_url
                            else:
                                url = post_page.url
                            
                            # Clean up
                            post_page.evaluate("document.getElementById('clipboard-input').remove()")
                        else:
                            url = post_page.url
                    else:
                        url = post_page.url
                    
                    print(f"Post URL: {url}")
                    
                    # Extract content
                    print("Extracting content...")
                    content_selectors = [
                        "div[class*='content']", 
                        "div[class*='desc']", 
                        "article",
                        "div[class*='note-detail'] div[class*='content']",
                        "div[class*='note-content']"
                    ]
                    
                    content = ""
                    for selector in content_selectors:
                        content_element = post_page.query_selector(selector)
                        if content_element:
                            extracted_content = content_element.text_content().strip()
                            if extracted_content and len(extracted_content) > len(content):
                                content = extracted_content
                                print(f"Found content using selector: {selector}")
                    
                    if not content:
                        content = "Content could not be extracted"
                    
                    # Extract comments
                    comments = []
                    comment_elements = post_page.query_selector_all("div[class*='comment-item'], div[class*='comment']")
                    for comment in comment_elements[:3]:
                        comments.append(comment.text_content())
                    
                    # Create post data
                    post_data = {
                        "title": title,
                        "url": url,
                        "content": content,
                        "comments": comments
                    }
                    
                    print(f"Successfully extracted post")
                    print(f"Title: {title}")
                    print(f"Content length: {len(content)} characters")
                    
                    # Summarize with OpenAI
                    print("Summarizing with OpenAI...")
                    analysis = summarize_with_openai(post_data)
                    
                    # Prepare data for Notion
                    notion_data = {
                        "title": post_data["title"],
                        "url": post_data["url"],
                        "category": analysis["category"],
                        "tags": analysis["tags"],
                        "summary": analysis["summary"]
                    }
                    
                    # Add to Notion
                    print("Adding to Notion...")
                    success = add_to_notion(notion_data)
                    
                    if success:
                        print("✅ Post successfully added to Notion")
                    else:
                        print("❌ Failed to add post to Notion")
                    
                    # Close the post page
                    post_page.close()
                    
                except Exception as e:
                    print(f"Error processing post: {e}")
                    # Close any extra pages
                    for pg in context.pages[1:]:
                        pg.close()
            
        except Exception as e:
            print(f"An error occurred in the test process: {e}")
        
        finally:
            print("\nClosing browser...")
            browser.close()
            print("Test script completed")

if __name__ == "__main__":
    run_test()







