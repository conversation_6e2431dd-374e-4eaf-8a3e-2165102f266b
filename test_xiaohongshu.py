import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_driver():
    """Set up and return a configured Chrome WebDriver"""
    chrome_options = Options()
    # Uncomment the line below if you want to run headless
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def check_login_status(driver):
    """Check if already logged in to Xiaohongshu"""
    driver.get("https://www.xiaohongshu.com/user/profile")
    time.sleep(3)
    
    # Check if we're on the login page or already logged in
    try:
        # Look for elements that would only appear when logged in
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'user-avatar') or contains(@class, 'profile-info')]"))
        )
        print("Already logged in to Xiaohongshu")
        return True
    except:
        print("Not logged in to Xiaohongshu")
        return False

def login_to_xiaohongshu(driver):
    """Login to Xiaohongshu account if not already logged in"""
    # First check if already logged in
    if check_login_status(driver):
        return
    
    # If not logged in, proceed with login
    driver.get("https://www.xiaohongshu.com/")
    print("Navigating to Xiaohongshu...")
    
    # Wait for login button to appear and click it
    try:
        login_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '登录')]"))
        )
        login_button.click()
        print("Clicked login button")
        
        # Wait for QR code to appear
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'qrcode') or contains(@class, 'login-qrcode')]"))
        )
        
        print("Please scan the QR code with your phone...")
        
        # Wait for user to scan QR code and complete login
        input("Press Enter after you've completed the login process...")
        
        # Verify login was successful
        if check_login_status(driver):
            print("Login successful")
        else:
            print("Login failed. Please try again.")
            
    except Exception as e:
        print(f"Login process encountered an error: {e}")
        input("Please complete the login manually and press Enter to continue...")

def navigate_to_liked_posts(driver):
    """Navigate to the liked posts page"""
    # The user is already on their profile page with the "点赞" tab selected
    # Just verify we're on the right page
    try:
        # Check if we're on the profile page
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), '点赞')]"))
        )
        print("Already on the liked posts page")
    except Exception as e:
        # If not, navigate to the profile page and click the liked tab
        print("Navigating to liked posts page...")
        driver.get("https://www.xiaohongshu.com/user/profile")
        time.sleep(3)
        
        try:
            liked_tab = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '点赞')]"))
            )
            liked_tab.click()
            print("Navigated to liked posts")
            time.sleep(3)
        except Exception as e:
            print(f"Failed to navigate to liked posts: {e}")
            # Alternative: directly navigate to liked posts URL
            driver.get("https://www.xiaohongshu.com/user/profile?tab=liked")
            time.sleep(3)

def get_liked_posts(driver, limit=2):
    """Extract information from liked posts"""
    posts = []
    
    try:
        # Find all post elements - update the selector based on the actual page structure
        post_elements = WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'note-item') or contains(@class, 'feed-item')]"))
        )
        
        print(f"Found {len(post_elements)} post elements")
        
        for i, post in enumerate(post_elements[:limit]):
            try:
                # Extract title - update selector based on actual structure
                try:
                    title = post.find_element(By.XPATH, ".//div[contains(@class, 'title') or contains(@class, 'desc')]").text
                except:
                    # If title not found, use a placeholder
                    title = f"Xiaohongshu Post #{i+1}"
                
                # Click on the post to open it
                post.click()
                time.sleep(3)
                
                # Switch to the new tab
                driver.switch_to.window(driver.window_handles[-1])
                url = driver.current_url
                print(f"Processing post: {url}")
                
                # Extract content
                try:
                    content_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'content') or contains(@class, 'desc')]"))
                    )
                    content = content_element.text
                except:
                    content = "Content could not be extracted"
                
                # Extract top comments
                comments = []
                try:
                    comment_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'comment-item') or contains(@class, 'reply-item')]")
                    for comment in comment_elements[:3]:  # Get top 3 comments
                        comments.append(comment.text)
                except:
                    pass
                
                posts.append({
                    "title": title,
                    "url": url,
                    "content": content,
                    "comments": comments
                })
                
                print(f"Successfully extracted post {i+1}")
                print(f"Title: {title}")
                print(f"URL: {url}")
                print(f"Content length: {len(content)} characters")
                print(f"Number of comments: {len(comments)}")
                
                # Close the tab and switch back
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                time.sleep(2)
                
            except Exception as e:
                print(f"Error processing post {i+1}: {e}")
                # If a tab was opened, close it and go back
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                
    except Exception as e:
        print(f"Failed to extract liked posts: {e}")
    
    return posts

def main():
    """Main function to run the test script"""
    driver = setup_driver()
    
    try:
        # Check if already logged in, and login if needed
        login_to_xiaohongshu(driver)
        
        # Navigate to liked posts
        navigate_to_liked_posts(driver)
        
        # Get liked posts (limit to 2 for testing)
        posts = get_liked_posts(driver, limit=2)
        print(f"\nTest completed. Found {len(posts)} posts")
        
        # Print summary of extracted posts
        for i, post in enumerate(posts):
            print(f"\nPost {i+1}:")
            print(f"Title: {post['title']}")
            print(f"URL: {post['url']}")
            print(f"Content length: {len(post['content'])} characters")
            print(f"Number of comments: {len(post['comments'])}")
            
    except Exception as e:
        print(f"An error occurred in the test process: {e}")
    
    finally:
        print("\nClosing browser...")
        driver.quit()
        print("Test script completed")

if __name__ == "__main__":
    main()