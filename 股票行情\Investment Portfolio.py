# -*- coding: utf-8 -*-
"""
Jing 的投资表（Holdings）实时估值脚本
- 读取 A 列 ETF 代码（A2 起）
- 读取  B列 份额（Shares）
- C 列 写实时价格（原币种）
- J 列 写 USDCAD 汇率（仅当原币种为 USD）
- D 列 写市值（CAD）
- 最后一行下一格写“总额（CAD）”
"""

import re
import math
import time
import json
import requests
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter

# ======= 1) 配置 =======
FILE_PATH = r"C:\Users\<USER>\Desktop\投资理财.xlsx"  # ← 改成你的文件
SHEET_NAME = "Holdings"

# A 列：Ticker；G 列：Shares；输出到 H/I/J 列
COL_TICKER = "A"
COL_SHARES = "B"
COL_PRICE  = "C"
COL_FX     = "J"
COL_VALUE  = "D"

# 这些常见加拿大全市场 ETF 如果没后缀，就默认补 ".TO"
TSX_GUESS = {
    "VDY","VIU","VEE","ZRE","XSB","ZAG","MNT","BOXX","VCNS","VCN","XEI","XIU","ZCN","XIC","VEQT","XEQT"
}

# ======= 2) 工具函数 =======
YF_QUOTE_URL = "https://query1.finance.yahoo.com/v7/finance/quote"

def yahoo_quotes(symbols):
    """批量取报价（延时/准实时），返回 {symbol: {price, currency}}"""
    if not symbols:
        return {}
    resp = requests.get(YF_QUOTE_URL, params={"symbols": ",".join(symbols)}, timeout=15)
    resp.raise_for_status()
    data = resp.json()["quoteResponse"]["result"]
    out = {}
    for q in data:
        sym = q.get("symbol")
        out[sym] = {
            "price": q.get("regularMarketPrice"),
            "currency": q.get("currency"),
            "name": q.get("longName") or q.get("shortName")
        }
    return out

def normalize_ticker(raw):
    """把 A 列的简写（如 VDY）规范成 Yahoo 可用代码（VDY.TO）"""
    if not isinstance(raw, str): 
        return None
    t = raw.strip().upper()
    if not t:
        return None
    if "." in t:  # 已带后缀
        return t
    # 如果在常见 TSX 列表里，默认加 .TO
    if t in TSX_GUESS:
        return t + ".TO"
    return t  # 其余保持原样（美股等）

def is_number(x):
    return isinstance(x, (int, float)) and not math.isnan(x)

# ======= 3) 读取工作表定位区间 =======
wb = load_workbook(FILE_PATH)
ws = wb[SHEET_NAME]

# 找到 A 列自第 2 行开始的连续数据区
start_row = 2
row = start_row
tickers_raw = []
while True:
    val = ws[f"{COL_TICKER}{row}"].value
    if val is None or str(val).strip() == "":
        break
    tickers_raw.append(str(val).strip())
    row += 1

end_row = start_row + len(tickers_raw) - 1
if end_row < start_row:
    raise RuntimeError("A 列没有可用的 ETF 代码（从 A2 起）。")

# 读取份额 B 列
shares = []
for r in range(start_row, end_row + 1):
    sh = ws[f"{COL_SHARES}{r}"].value
    try:
        sh = float(sh)
    except (TypeError, ValueError):
        sh = None
    shares.append(sh)

# 规范化 ticker
norm = [normalize_ticker(t) for t in tickers_raw]

# ======= 4) 拉取行情 + 汇率 =======
try:
    # 先拿所有 symbol 的报价
    quote_map = yahoo_quotes([t for t in norm if t])
    print(f"✅ 成功获取 {len(quote_map)} 个股票报价")

    # 拿 USDCAD 汇率，用于把 USD 金额换算为 CAD
    fx_quote = yahoo_quotes(["USDCAD=X"])
    USDCAD = fx_quote.get("USDCAD=X", {}).get("price")
    if USDCAD:
        print(f"✅ USDCAD 汇率: {USDCAD:.4f}")
    else:
        print("⚠️ 无法获取 USDCAD 汇率")
except Exception as e:
    print(f"❌ 获取行情数据失败: {e}")
    raise

# ======= 5) 逐行计算并写回 =======
total_cad = 0.0

for i, r in enumerate(range(start_row, end_row + 1)):
    sym = norm[i]
    sh  = shares[i]
    ticker_raw = tickers_raw[i]

    q = quote_map.get(sym, {})
    price = q.get("price")
    currency = q.get("currency")

    print(f"处理 {ticker_raw} ({sym}): 价格={price}, 币种={currency}, 份额={sh}")

    # 写回价格
    ws[f"{COL_PRICE}{r}"].value = price

    # 计算市值（CAD）
    fx_used = None
    value_cad = None
    if is_number(price) and is_number(sh):
        if currency == "USD":
            fx_used = USDCAD
            if is_number(USDCAD):
                value_cad = price * sh * USDCAD
                print(f"  USD转CAD: {price} * {sh} * {USDCAD:.4f} = {value_cad:.2f}")
            else:
                print(f"  ⚠️ 无法获取USDCAD汇率，跳过USD资产")
        elif currency == "CAD" or currency is None:
            # CAD 或未知币种默认按 CAD 处理
            value_cad = price * sh
            print(f"  CAD计算: {price} * {sh} = {value_cad:.2f}")
        else:
            # 其他币种暂时按原价处理（可根据需要添加更多汇率）
            value_cad = price * sh
            print(f"  其他币种({currency}): {price} * {sh} = {value_cad:.2f}")
    else:
        print(f"  ⚠️ 价格或份额数据无效，跳过")

    # 写回汇率（仅当 USD）
    ws[f"{COL_FX}{r}"].value = fx_used

    # 写回市值 CAD
    ws[f"{COL_VALUE}{r}"].value = value_cad

    if is_number(value_cad):
        total_cad += value_cad

# ======= 6) 写入总额 =======
sum_row = end_row + 1
# 在总额行的前一列写标签，在D列写总额数值
ws[f"C{sum_row}"].value = "总额 (CAD)"
ws[f"{COL_VALUE}{sum_row}"].value = total_cad

# 可选：在 H/I/J 列加上表头（如果你还没建）
ws[f"{COL_PRICE}{start_row-1}"].value = "实时价格"
ws[f"{COL_FX}{start_row-1}"].value    = "USDCAD"
ws[f"{COL_VALUE}{start_row-1}"].value = "市值(CAD)"
ws[f"{COL_SHARES}{start_row-1}"].value= "Shares(份额)"

try:
    wb.save(FILE_PATH)
    print(f"✅ Done. 估值写回 {SHEET_NAME}! 总额(CAD) = {total_cad:,.2f}")
    print(f"📊 共处理 {len(tickers_raw)} 个资产")
except Exception as e:
    print(f"❌ 保存文件失败: {e}")
    raise
